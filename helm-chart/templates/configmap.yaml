apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "name" . }}
  namespace: {{ .Values.namespace }}
data:
  {{- with .Values.configmap.application }}
  run.sh: |
    #!/bin/bash
    clear
    # application jar path
    JAR_APP_PATH={{ .JAR_APP_PATH }} \
    # env conf variables
      DATASOURCE_USERNAME=portfolio\
      DATASOURCE_HOST={{ .db_host }}\
      DATASOURCE_REPLICA_HOSTS={{ .replica_db_host }}\
      DATASOURCE_USERNAME=portfolio\
      DATASOURCE_REPLICA_USERS=portfolio\
      DATASOURCE_PORT={{ .db_port }}\
      DATASOURCE_REPLICA_PORTS={{ .replica_port }}\
      DATASOURCE_PASSWORD={{ .db_pass }}\
      DATASOURCE_REPLICA_PASSWORDS={{ .db_pass }}\
      DATASOURCE_SCHEMA=portfolio\
      DATASOURCE_DB=portfolio\
      ENABLE_FLYWAY=true\
      ENABLE_SWAGGER={{ .ENABLE_SWAGGER | default "false" }}\
      SERVER_PATH=app\
      CLICK_HOUSE_HOST={{ .clickhouse_host }}\
      CLICK_HOUSE_PORT={{ .clickhouse_port }}\
      CLICK_HOUSE_LOGIN={{ .click_login }}\
      CLICK_HOUSE_PASSWORD={{ .click_pass }}\
      CLICK_HOUSE_SCHEMA=portfolio_db\
      CLICK_HOUSE_TABLE=distributed_learner_portfolio\
      CLICK_HOUSE_PROFTECH_TABLE=distributed_proftech_portfolio\
      CLICK_HOUSE_D_NOTIFICATION_TABLE=distributed_notifications\
      CLICK_HOUSE_LESSONS_TABLE=distributed_lessons\
      CLICK_HOUSE_NOTIFICATION_TABLE=MATDiagnosticNotificationsView\
      CLICK_HOUSE_GENERAL_REGION_RATING=distributed_diagnostic_general_region_rating\
      CLICK_HOUSE_GENERAL_SCHOOL_RATING=distributed_diagnostic_general_school_rating\
      CLICK_HOUSE_INTERNAL_REGION_LEVEL=distributed_diagnostic_internal_region_level\
      CLICK_HOUSE_INTERNAL_REGION_PERSON=distributed_diagnostic_internal_region_person\
      CLICK_HOUSE_INTERNAL_REGION_RATING=distributed_diagnostic_internal_region_rating\
      CLICK_HOUSE_INTERNAL_SCHOOL_LEVEL=distributed_diagnostic_internal_school_level\
      CLICK_HOUSE_INTERNAL_SCHOOL_PERSON=distributed_diagnostic_internal_school_person\
      CLICK_HOUSE_INTERNAL_SCHOOL_RATING=distributed_diagnostic_internal_school_rating\
      CLICK_HOUSE_MARKS_AVERAGE=distributed_marks_average \
      CLICK_HOUSE_MARKS_FINAL=distributed_marks_final \
      CLICK_HOUSE_CLUSTER=portfolio_cluster \
      CLICK_HOUSE_NOTIFICATION_OPTIMIZE=true \
      CLICKHOUSE_LOGS_ENABLED={{ .click_logs_state }}\
      CLICKHOUSE_USE_MATERIAL_VIEWS={{ .click_mv_state }}\
      CLICK_HOUSE_API_KEY={{ .click_api_key }}\
      CLICK_HOUSE_CONNECTOR_FAIL_ON_ERROR=false\
      CLICK_HOUSE_CONNECTOR_HOST={{ .click_connector_host }}\
      CLICK_HOUSE_CONNECTOR_ENABLE=false \
      CLICK_HOUSE_CONNECTION_TIMEOUT=3000 \
      CLICK_HOUSE_KEEP_ALIVE_TIMEOUT=3000 \
      CLICK_HOUSE_TIME_TO_LIVE_TIMEOUT=3000 \
      CLICK_HOUSE_SOCKET_TIMEOUT=3000\
      CLICK_HOUSE_DATA_TRANSFER_TIMEOUT=3000 \
      CONTINGENT_HOST={{ .contingent_api_url }}\
      CONTINGENT_APP={{ .contingent_api_key }}\
      CONTINGENT_HOST_PATIENT={{ .contingent_source }}\
      PROFORIENTATION_APP={{ .proforientation_api_key }}\
      ESZ_HOST={{ .esz_host }}\
      PORTFOLIO_LINK={{ .portfolio_link_qr_code }}\
      NSI_HOST={{ .nsi_api_url }}\
      NSI_APP=portfolio\
      NSI_KKY={{ .nsi_api_key }}\
      NSI_ID={{ .nsi_id }}\
      NSI_CACHE_DURATION_MINUTES={{ .nsi_cache_duration_minutes }}\
      NSI_CACHE_MAX_ITEMS={{ .nsi_cache_maxitems }}\
      CONTINGENT_HOST_CLASSES={{ .contingent_classes_host_url }}\
      OLD_CONTINGENT_CATEGORY_ID=1\
      NEW_CONTINGENT_CATEGORY_ID=1\
      ORGANIZATION_REGISTRY_CODE={{ .organisation_registry_code }}\
      STAFF_REGISTRY_CODE={{ .staff_registry_code }}\
      AUPD_URL={{ .aupd_url }}\
      AUPD_SUBSYSTEMID=4\
      AUPD_TOKEN_ISSUER={{ .aupd_token }}\
      AUPD_KEY={{ .aupd_api_key }}\
      AUPD_CERT={{ .aupd_cert_name }}\
      ATTACHMENTS_CEDS={{ .ATTACHMENTS_CEDS }}\
      CEDS_HOST={{ .ceds_url }}\
      CEDS_SYSTEM_CODE=ais_portfolio\
      CEDS_PASSWORD={{ .ceds_pass }}\
      CEDS_REPOSITORY=DIT\
      S3_KEY_ID={{ .s3_key_id }}\
      S3_KEY_SECRET={{ .s3_key_secret }}\
      S3_BUCKET_NAME={{ .s3_bucket_name }}\
      S3_ADDRESS={{ .s3_address }}\
      OLD_AUTH_ENABLED=false\
      OPERATOR_GLOBAL_ROLE_ID=19\
      SCHOOL_ADMIN_GLOBAL_ROLE_ID=26\
      HEAD_TEACHER_LOCAL_ROLE_ID=8\
      TEACHER_LOCAL_ROLE_ID=9\
      AGENT_GLOBAL_ROLE_ID=2\
      ADMIN_GLOBAL_ROLE_ID=16\
      EMPLOYEE_GLOBAL_ROLE_ID=3\
      STUDENT_GLOBAL_ROLE_ID=1\
      CRON_UPDATE_VIEW="{{ .cron_update_job }}"\
      FOS_KKY=Zm9zX2V4dF9mb2xpb18yMDIwXzE=\
      FOS_URL={{ .fos_url }}/fos/api/fos/v1/url/\
      FOS_PRODUCT=portfolio\
      FOS_PATH=/fos/portfolio\
      KAFKA_USERNAME={{ .KAFKA_USERNAME }}\
      KAFKA_PASSWORD={{ .KAFKA_PASSWORD }}\
      ESZ_TKN="{{ .ESZ_X_API_KEY }}"\
      ESZ_KKY={{ .ESZ_KKY }}\
      IMPORT_FILE_MAX_ROWS_COUNT={{ .IMPORT_FILE_MAX_ROWS_COUNT }}\
      IMPORT_DATA_PARTITION_SIZE={{ .IMPORT_DATA_PARTITION_SIZE }}\
      KAFKA_EDUCATION_TOPIC={{ .KAFKA_EDUCATION_TOPIC }}\
      KAFKA_EDUCATION_GROUP_ID={{ .KAFKA_EDUCATION_GROUP_ID }}\
      KAFKA_EDUCATION_BOOTSTRAP_SERVERS={{ .KAFKA_BOOTSTRAP_SERVERS }}\
      KAFKA_BOOTSTRAP_SERVERS={{ .KAFKA_BOOTSTRAP_SERVERS }}\
      KAFKA_TOPIC={{ .KAFKA_TOPIC }}\
      KAFKA_PRODUCER_ENABLED={{ .KAFKA_PRODUCER_ENABLED }}\
      KAFKA_CONSUMER_ENABLED={{ .KAFKA_CONSUMER_ENABLED }}\
      GRATITUDE_TEACHER_TOPIC=portfolio.fct.gratitude-achievements.0\
      KAFKA_NOTIFICATION_TOPIC=eks.cmd.portfolio.0\
      NOTIFICATIONS_CLICKHOUSE_LIMIT=150\
      NOTIFICATIONS_URL={{ .notifications_url }}\
      NOTIFICATIONS_CLICKHOUSE_DAYS_OLD={{ .NOTIFICATIONS_CLICKHOUSE_DAYS_OLD }}\
      CRON_SEND_GRATITUDE_TEACHER="0 0 0/2 ? * *"\
      CRON_READ_CLICKHOUSE_DIAGNOSTIC="0 0/30 * ? * *"\
      CRON_SEND_NOTIFICATION="0 15/30 * ? * *"\
      CRON_CHECK_FAIL_EXCEL="0 0 0 ? * *"\
      CONNECTION_POOL_SIZE={{ .CONNECTION_POOL_SIZE }}\
      LEAK_DETECTION_THRESHOLD=60000\
      CONNECTION_HANDLING_MODE=DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION\
      REST_TIMEOUT_TIME={{ .REST_TIMEOUT_TIME }}\
      REST_READ_TIMEOUT_TIME={{ .REST_READ_TIMEOUT_TIME }}\
      REST_TIMEOUT_TIME_LOW={{ .REST_TIMEOUT_TIME_LOW }}\
      REST_READ_TIMEOUT_TIME_LOW={{ .REST_READ_TIMEOUT_TIME_LOW }}\
      REST_TIMEOUT_TIME_HIGH={{ .REST_TIMEOUT_TIME_HIGH }}\
      REST_READ_TIMEOUT_TIME_HIGH={{ .REST_READ_TIMEOUT_TIME_HIGH }}\
      MIGRATION_REGIONS={{ .MIGRATION_REGIONS }}\
      SCHOOL_HOST={{ .SCHOOL_HOST }}\
      SCHOOL_RATING_ENABLED={{ .SCHOOL_RATING_ENABLED }}\
      KAFKA_MAX_POLL_RECORDS={{ .KAFKA_MAX_POLL_RECORDS }}\
      KAFKA_MAX_POLL_INTERVAL_MS={{ .KAFKA_MAX_POLL_INTERVAL_MS }}\
      KAFKA_SESSION_TIMEOUT_MS={{ .KAFKA_SESSION_TIMEOUT_MS }}\
      KAFKA_EDUCATION_AUTO_STARTUP={{ .KAFKA_EDUCATION_AUTO_STARTUP }}\
      KAFKA_NOTIFICATION_PUSH_ENABLED=true\
      KAFKA_NOTIFICATION_EMAIL_ENABLED=false\
      CONTEXT_UPDATE_EDUCATIONS={{ .CONTEXT_UPDATE_EDUCATIONS }}\
      CONTEXT_ACTUALIZE_PERSONS={{ .CONTEXT_ACTUALIZE_PERSONS }}\
      DOP_URL=https://***********/api/mes-integration/event/\
      DOP_TOKEN='Bearer 6234faed42a7e2dd371d607f33d0256f526e1c4e7ee36349f7e2ba3ce78ad163'\
      PROF_URL={{ .PROF_URL }}\
      PROF_AUTH_ACTION=User/Authentication\
      PROF_LOGIN=dit\
      PROF_PASSWORD=0OCmooId3Abv\
      HIKARI_LOGBACK_LEVEL=DEBUG\
      IN_OUT_LOGGING_INTERCEPTOR=true\
      HTTP_MAX_PER_ROUTE=100\
      HTTP_MAX_TOTAL=200\
      MELODY_ENABLED={{ .MELODY_ENABLED }}\
      MELODY_LOGIN=portfolio\
      MELODY_PASSWORD={{ .MELODY_PASSWORD }}\
      MELODY_LOG=false\
      JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8\
      java {{ .xmx }}\
      -jar ${JAR_APP_PATH} --server.port=8080
  {{ end }}
