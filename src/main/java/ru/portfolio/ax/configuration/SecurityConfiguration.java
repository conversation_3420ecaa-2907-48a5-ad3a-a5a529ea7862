package ru.portfolio.ax.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/v2/api-docs",
                "/configuration/ui",
                "/swagger-resources/**",
                "/configuration/security",
                "/swagger-ui.html",
                "app/swagger-ui.html",
                "/app/swagger-ui.html",
                "/webjars/**");
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .exceptionHandling()
                .and().authorizeRequests()
                .antMatchers("/login", "/auth/login", "/logout", "/auth/logout").permitAll()
                .antMatchers("/actuator/prometheus").access(
                                "hasIpAddress('10.0.0.0/8') or " +       // Kubernetes cluster IP range
                                "hasIpAddress('127.0.0.1') or " +        // Localhost
                                "hasIpAddress('::1')"                    // IPv6 localhost
                )
                .anyRequest().anonymous()
                .and()
                .formLogin();
    }
}
